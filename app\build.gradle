plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.example.floatingai'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.floatingai"
        minSdk 30
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    
    // HTTP requests para OpenRouter API
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // Para manejo de imágenes
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    
    // Para JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}